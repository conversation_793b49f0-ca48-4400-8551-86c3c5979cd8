{"name": "store-manager", "version": "1.0.0", "private": true, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "scripts": {"lint": "next lint", "build": "next build", "production": "next start", "flint": "npm run format && npm run lint", "nextjs": "npm install && npm run flint && next dev", "fastify": "tsx watch --env-file=.env server/fastify.ts", "format": "prettier --check --write \"{app,demo,layout,types}/**/*.{js,ts,tsx,d.ts}\"", "start": "concurrently \"npm:fastify\" \"npm:nextjs\""}, "dependencies": {"@dimaskiddo/angka-terbilang-nodejs": "^1.0.9", "@fastify/cors": "^11.0.1", "@fastify/redis": "^7.0.2", "@types/node": "^24.0.10", "@types/react": "18.2.12", "@types/react-dom": "18.2.5", "bufferutil": "^4.0.9", "canvas": "^3.1.2", "chart.js": "4.2.1", "cloudinary": "^2.6.1", "concurrently": "^9.2.0", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "fastify": "^5.4.0", "fastify-cron": "^1.4.0", "ioredis": "^5.6.1", "isomorphic-dompurify": "^2.25.0", "lodash": "^4.17.21", "mongoose": "^8.17.0", "next": "13.5.9", "next-pwa": "^5.6.0", "primeflex": "^3.3.1", "primeicons": "^6.0.1", "primereact": "10.2.1", "puppeteer": "^24.12.1", "react": "18.2.0", "react-dom": "18.2.0", "tsx": "^4.20.3", "typescript": "^5.8.3", "unix-print": "^1.3.2", "utf-8-validate": "^6.0.5", "uuid": "^11.1.0", "valibot": "^1.1.0", "zustand": "^5.0.4"}, "devDependencies": {"@types/crypto-js": "^4.2.2", "@types/lodash": "^4.17.16", "@types/mongoose": "^5.11.97", "@types/next-pwa": "^5.6.9", "@types/react-transition-group": "^4.4.12", "eslint": "8.43.0", "eslint-config-next": "13.4.6", "prettier": "^2.8.8", "sass": "^1.63.4"}}